import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth/config"
import { hasPermission, UserRole } from "@/lib/validations/user"

export async function withAuth(
  request: NextRequest,
  handler: (request: NextRequest, user: any) => Promise<NextResponse>
) {
  try {
    // Temporairement désactivé pour le débogage
    // const session = await auth.api.getSession({
    //   headers: request.headers
    // })

    // if (!session?.user) {
    //   return NextResponse.json(
    //     { success: false, error: "Non autorisé" },
    //     { status: 401 }
    //   )
    // }

    // Utilisateur fictif pour les tests
    const mockUser = {
      id: "test-user",
      email: "<EMAIL>",
      role: "ADMIN"
    }

    return handler(request, mockUser)
  } catch (error) {
    return NextResponse.json(
      { success: false, error: "Erreur d'authentification" },
      { status: 401 }
    )
  }
}

export async function withPermission(
  request: NextRequest,
  permission: string,
  handler: (request: NextRequest, user: any) => Promise<NextResponse>
) {
  return withAuth(request, async (req, user) => {
    const userRole = user.role as UserRole
    
    if (!hasPermission(userRole, permission)) {
      return NextResponse.json(
        { success: false, error: "Permissions insuffisantes" },
        { status: 403 }
      )
    }

    return handler(req, user)
  })
}

export async function withRole(
  request: NextRequest,
  allowedRoles: UserRole[],
  handler: (request: NextRequest, user: any) => Promise<NextResponse>
) {
  return withAuth(request, async (req, user) => {
    const userRole = user.role as UserRole
    
    if (!allowedRoles.includes(userRole)) {
      return NextResponse.json(
        { success: false, error: "Rôle insuffisant" },
        { status: 403 }
      )
    }

    return handler(req, user)
  })
}
