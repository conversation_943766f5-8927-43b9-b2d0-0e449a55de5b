"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { usePermissions } from "@/lib/hooks/use-permissions"
import {
  LayoutDashboard,
  FileText,
  Users,
  Building2,
  Settings,
  ClipboardList,
  BarChart3,
  ChevronLeft,
  ChevronRight
} from "lucide-react"

interface SidebarItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  badge?: string
  permission?: string
}

const sidebarItems: SidebarItem[] = [
  {
    title: "Tableau de bord",
    href: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "Audits",
    href: "/audits",
    icon: ClipboardList,
    permission: "audits:read",
  },
  {
    title: "Rapports",
    href: "/reports",
    icon: FileText,
    permission: "reports:read",
  },
  {
    title: "Organisations",
    href: "/organizations",
    icon: Building2,
    permission: "users:read", // Les organisations sont liées à la gestion des utilisateurs
  },
  {
    title: "Utilisateurs",
    href: "/admin/users",
    icon: Users,
    permission: "users:read",
  },
  {
    title: "Statistiques",
    href: "/analytics",
    icon: BarChart3,
    permission: "users:read", // Les stats nécessitent au moins la lecture des utilisateurs
  },
  {
    title: "Paramètres",
    href: "/settings",
    icon: Settings,
  },
]

interface DashboardSidebarProps {
  className?: string
}

export function DashboardSidebar({ className }: DashboardSidebarProps) {
  const [collapsed, setCollapsed] = useState(false)
  const pathname = usePathname()

  // Temporairement désactivé pour éviter les erreurs d'auth
  // const { checkPermission } = usePermissions()
  const checkPermission = () => true // Temporaire : autoriser tout

  return (
    <div
      className={cn(
        "magneto-sidebar border-r border-sidebar-border transition-all duration-300",
        collapsed ? "w-16" : "w-64",
        className
      )}
    >
      <div className="flex h-full flex-col">
        {/* Header */}
        <div className="flex h-16 items-center justify-between px-4 border-b border-sidebar-border">
          {!collapsed && (
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-[#2E427D] rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">M</span>
              </div>
              <span className="font-semibold text-white">Magneto</span>
            </div>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCollapsed(!collapsed)}
            className="text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
          >
            {collapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 space-y-1 p-2">
          {sidebarItems.map((item) => {
            // Check permissions if required
            if (item.permission && !checkPermission(item.permission)) {
              return null
            }

            const isActive = pathname === item.href || pathname.startsWith(item.href + "/")
            const Icon = item.icon

            return (
              <Link key={item.href} href={item.href}>
                <Button
                  variant="ghost"
                  className={cn(
                    "w-full justify-start text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                    isActive && "magneto-sidebar-selected",
                    collapsed && "px-2"
                  )}
                >
                  <Icon className={cn("h-4 w-4", !collapsed && "mr-2")} />
                  {!collapsed && (
                    <span className="truncate">{item.title}</span>
                  )}
                  {!collapsed && item.badge && (
                    <span className="ml-auto text-xs bg-sidebar-accent text-sidebar-accent-foreground px-1.5 py-0.5 rounded">
                      {item.badge}
                    </span>
                  )}
                </Button>
              </Link>
            )
          })}
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-sidebar-border">
          {!collapsed && (
            <div className="text-xs text-sidebar-foreground/60">
              Magneto v1.0.0
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
